// Debug-Version des fetchHourBalances Moduls mit ausführlichem Logging

function fetchHourBalancesDebug() {
  console.log("=== DEBUG: fetchHourBalances gestartet ===");
  
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName("Config");
  const reportingSheet = SpreadsheetApp.openById(OPS_REPORTING);
  const sourceSheet = reportingSheet.getSheetByName("iTIMEw");

  console.log("✓ Sheets erfolgreich geöffnet");
  console.log("- Config Sheet:", configSheet.getName());
  console.log("- Source Sheet:", sourceSheet.getName());

  const configData = configSheet.getRange(1, 1, configSheet.getLastRow() - 1, 2).getValues();
  const sourceValues = sourceSheet.getDataRange().getValues();

  console.log("✓ Daten geladen");
  console.log("- Config Einträge:", configData.length);
  console.log("- Source Zeilen:", sourceValues.length);

  const resultMap = new Map();

  // Helper function to convert date to week number
  function getWeekNumber(date) {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
    const week1 = new Date(d.getFullYear(), 0, 4);
    return 1 + Math.round(((d.getTime() - week1.getTime()) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
  }

  configData.forEach(([country, location], configIndex) => {
    if (!country || !location) return;

    console.log(`\n--- Processing Config ${configIndex + 1}: ${country} / ${location} ---`);

    // Aggregate data by week for each metric
    const weeklyAggregates = new Map();
    let processedRows = 0;
    let matchingRows = 0;

    sourceValues.forEach((row, rowIndex) => {
      processedRows++;
      
      const rowCountry = row[8]; // Column I (0-indexed: 8)
      const rowLocation = row[9]; // Column J (0-indexed: 9)
      const dateValue = row[6];   // Column G (0-indexed: 6)
      const role = row[10];       // Column K (0-indexed: 10)

      // Debug erste 3 Zeilen für jede Config
      if (rowIndex < 3) {
        console.log(`  Row ${rowIndex}: Country=${rowCountry}, Location=${rowLocation}, Role=${role}, Date=${dateValue}`);
      }

      if (rowCountry === country && rowLocation === location && role === "Runner") {
        matchingRows++;
        
        const week = getWeekNumber(dateValue);
        const key = `${rowCountry}_${rowLocation}_${week}`;

        if (!weeklyAggregates.has(key)) {
          weeklyAggregates.set(key, {
            country: rowCountry,
            location: rowLocation,
            week: week,
            cumulativeBalance: 0,
            cumulativeNegBalance: 0,
            cumulativePosBalance: 0,
            belowNegThreshold: 0,
            abovePosThreshold: 0,
            peopleBelowNegThreshold: 0,
            peopleAbovePosThreshold: 0,
            rowCount: 0
          });
          console.log(`  ✓ Neue Woche erstellt: ${key} (KW ${week})`);
        }

        const aggregate = weeklyAggregates.get(key);
        const balanceValue = row[24] === "" ? 0 : parseFloat(row[24]); // Column Y (0-indexed: 24)
        const peopleCount = row[26] === "" ? 0 : parseFloat(row[26]);  // Column AA (0-indexed: 26)
        const isNegative = row[13]; // Column N (0-indexed: 13)
        const isBelowNegThreshold = row[14]; // Column O (0-indexed: 14)
        const isAbovePosThreshold = row[15]; // Column P (0-indexed: 15)

        // Debug erste passende Zeile
        if (aggregate.rowCount === 0) {
          console.log(`  Erste Zeile für ${key}:`);
          console.log(`    Balance: ${balanceValue}, People: ${peopleCount}`);
          console.log(`    isNegative: ${isNegative}, isBelowNegThreshold: ${isBelowNegThreshold}, isAbovePosThreshold: ${isAbovePosThreshold}`);
        }

        // Cumulative balance (no filter)
        aggregate.cumulativeBalance += balanceValue;

        // Cumulative negative balance (N = FALSE)
        if (isNegative === false) {
          aggregate.cumulativeNegBalance += balanceValue;
        }

        // Cumulative positive balance (N = TRUE)
        if (isNegative === true) {
          aggregate.cumulativePosBalance += balanceValue;
        }

        // Below negative threshold (O = TRUE)
        if (isBelowNegThreshold === true) {
          aggregate.belowNegThreshold += balanceValue;
          aggregate.peopleBelowNegThreshold += peopleCount;
        }

        // Above positive threshold (P = TRUE)
        if (isAbovePosThreshold === true) {
          aggregate.abovePosThreshold += balanceValue;
          aggregate.peopleAbovePosThreshold += peopleCount;
        }

        aggregate.rowCount++;
      }
    });

    console.log(`  Verarbeitete Zeilen: ${processedRows}`);
    console.log(`  Passende Zeilen: ${matchingRows}`);
    console.log(`  Wochen gefunden: ${weeklyAggregates.size}`);

    // Convert aggregates to result format
    for (const [key, aggregate] of weeklyAggregates.entries()) {
      console.log(`  Woche ${aggregate.week} Ergebnisse:`);
      console.log(`    Cumulative Balance: ${aggregate.cumulativeBalance}`);
      console.log(`    Cumulative Neg: ${aggregate.cumulativeNegBalance}`);
      console.log(`    Cumulative Pos: ${aggregate.cumulativePosBalance}`);
      console.log(`    Below Neg Threshold: ${aggregate.belowNegThreshold}`);
      console.log(`    Above Pos Threshold: ${aggregate.abovePosThreshold}`);
      console.log(`    People Below Neg: ${aggregate.peopleBelowNegThreshold}`);
      console.log(`    People Above Pos: ${aggregate.peopleAbovePosThreshold}`);
      console.log(`    Zeilen verarbeitet: ${aggregate.rowCount}`);

      const rowData = [
        aggregate.country,
        aggregate.location,
        aggregate.week,
        null, // Sum deliveries (filled by fetchSlotClosings)
        null, // Weighted slot closing hrs (filled by fetchSlotClosings)
        aggregate.cumulativeBalance,
        aggregate.cumulativeNegBalance,
        aggregate.cumulativePosBalance,
        aggregate.belowNegThreshold,
        aggregate.abovePosThreshold,
        aggregate.peopleBelowNegThreshold,
        aggregate.peopleAbovePosThreshold,
        null, null, null, null, null, null // Retention metrics (filled by fetchRetention)
      ];

      resultMap.set(key, rowData);
    }
  });

  console.log(`\n=== DEBUG: fetchHourBalances abgeschlossen ===`);
  console.log(`Gesamt Ergebnisse: ${resultMap.size}`);
  
  return resultMap;
}

// Test-Funktion für einzelne Country/Location
function testSingleLocation(testCountry, testLocation) {
  console.log(`=== TEST: Einzelner Standort ${testCountry}/${testLocation} ===`);
  
  const reportingSheet = SpreadsheetApp.openById(OPS_REPORTING);
  const sourceSheet = reportingSheet.getSheetByName("iTIMEw");
  const sourceValues = sourceSheet.getDataRange().getValues();
  
  let foundRows = 0;
  
  sourceValues.forEach((row, index) => {
    const rowCountry = row[8];
    const rowLocation = row[9];
    const role = row[10];
    
    if (rowCountry === testCountry && rowLocation === testLocation && role === "Runner") {
      foundRows++;
      if (foundRows <= 5) { // Zeige nur erste 5 Treffer
        console.log(`Zeile ${index}: Date=${row[6]}, Balance=${row[24]}, People=${row[26]}, N=${row[13]}, O=${row[14]}, P=${row[15]}`);
      }
    }
  });
  
  console.log(`Gefundene Zeilen für ${testCountry}/${testLocation}: ${foundRows}`);
}

// Funktion zum Testen der Woche-Konvertierung
function testWeekConversion() {
  console.log("=== TEST: Woche-Konvertierung ===");

  function getWeekNumber(date) {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
    const week1 = new Date(d.getFullYear(), 0, 4);
    return 1 + Math.round(((d.getTime() - week1.getTime()) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
  }

  const testDates = [
    new Date("2024-01-01"),
    new Date("2024-01-15"),
    new Date("2024-06-15"),
    new Date("2024-12-31")
  ];

  testDates.forEach(date => {
    const week = getWeekNumber(date);
    console.log(`${date.toISOString().split('T')[0]} -> KW ${week}`);
  });
}

// Schnell-Test für die wichtigsten Funktionen
function quickTest() {
  console.log("=== QUICK TEST ===");

  try {
    // Test 1: Datenquelle-Zugriff
    console.log("Test 1: Datenquelle-Zugriff...");
    const reportingSheet = SpreadsheetApp.openById(OPS_REPORTING);
    const sourceSheet = reportingSheet.getSheetByName("iTIMEw");
    console.log("✓ iTIMEw Sheet erfolgreich geöffnet");

    // Test 2: Datenstruktur
    console.log("Test 2: Datenstruktur...");
    const sourceValues = sourceSheet.getDataRange().getValues();
    console.log(`✓ ${sourceValues.length} Zeilen geladen`);

    if (sourceValues.length > 1) {
      const headerRow = sourceValues[0];
      console.log(`Spalte I (Country): ${headerRow[8]}`);
      console.log(`Spalte J (Location): ${headerRow[9]}`);
      console.log(`Spalte K (Role): ${headerRow[10]}`);
      console.log(`Spalte Y (Balance): ${headerRow[24]}`);
      console.log(`Spalte AA (People): ${headerRow[26]}`);
    }

    // Test 3: Config-Zugriff
    console.log("Test 3: Config-Zugriff...");
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const configSheet = ss.getSheetByName("Config");
    const configData = configSheet.getRange(1, 1, configSheet.getLastRow() - 1, 2).getValues();
    console.log(`✓ ${configData.length} Config-Einträge gefunden`);

    configData.slice(0, 3).forEach(([country, location], index) => {
      console.log(`Config ${index + 1}: ${country} / ${location}`);
    });

    console.log("✓ Quick Test erfolgreich abgeschlossen");

  } catch (error) {
    console.error("❌ Fehler im Quick Test:", error.toString());
  }
}

# Testing Guide für das überarbeitete KPI Review Sheet Script

## Vorbereitung

### 1. Backup erstellen
- Erstellen Sie eine Kopie Ihres aktuellen Google Sheets
- Notieren Sie sich die aktuellen Daten im "Data" Sheet für Vergleiche

### 2. Script-Deployment
- Öffnen Sie Google Apps Script Editor
- Ersetzen Sie den Code mit dem überarbeiteten Script aus `kpi_review_sheet_backup.js`
- Speichern Sie das Script

## Test-Schritte

### Phase 1: Grundlegende Funktionalität

#### Test 1.1: Script-Ausführung
```
1. Führen Sie fetchKPIs() aus
2. Prüfen Sie auf Fehler in der Konsole
3. Erwartung: Script läuft ohne Fehler durch
```

#### Test 1.2: Datenquelle-Zugriff
```
1. Pr<PERSON><PERSON> Sie, ob das Script auf iTIMEw Sheet zugreifen kann
2. Erwartung: Keine Berechtigungsfehler
3. <PERSON>hler: Prüfen Sie OPS_REPORTING Sheet-ID und Berechtigungen
```

### Phase 2: Datenvalidierung

#### Test 2.1: Header-Struktur
```
1. Prüfen Sie Zeile 1 im "Data" Sheet
2. Erwartete Header (18 Spalten):
   - A: Country
   - B: Hub  
   - C: Week
   - D: Sum deliveries
   - E: Weighted slot closing hrs.
   - F: Cumulative balances
   - G: Cumulative neg. balances
   - H: Cumulative pos. balances
   - I: Below neg. threshold
   - J: Above pos. threshold
   - K: People below neg. threshold
   - L: People above pos. threshold
   - M: R 12 week pool retention
   - N: R 8 week pool retention
   - O: R 8 week hire retention
   - P: R+ 12 week pool retention
   - Q: R+ 8 week pool retention
   - R: R+ 8 week hire retention
```

#### Test 2.2: Datenzeilen
```
1. Prüfen Sie, ob Datenzeilen vorhanden sind
2. Prüfen Sie, ob die Sortierung nach Week funktioniert
3. Erwartung: Daten sind nach Spalte C (Week) aufsteigend sortiert
```

### Phase 3: Hour Balances Validierung

#### Test 3.1: Neue Datenquelle
```
1. Öffnen Sie das iTIMEw Sheet manuell
2. Suchen Sie nach Testdaten für ein bekanntes Country/Location
3. Prüfen Sie, ob die Filterkriterien korrekt sind:
   - Spalte I (Country) = Ihr Test-Country
   - Spalte J (Location) = Ihr Test-Location  
   - Spalte K (Role) = "Runner"
```

#### Test 3.2: Woche-Konvertierung
```
1. Nehmen Sie ein Datum aus Spalte G im iTIMEw Sheet
2. Berechnen Sie manuell die Kalenderwoche
3. Vergleichen Sie mit dem Wert in Spalte C im Data Sheet
4. Erwartung: Korrekte KW-Konvertierung
```

#### Test 3.3: Aggregation der Metriken
```
Für jede Metrik einzeln testen:

Test 3.3.1: Cumulative balances (Spalte F)
- Summe aller Werte aus iTIMEw Spalte Y (ohne Filter)
- Erwartung: Korrekte Summe

Test 3.3.2: Cumulative neg. balances (Spalte G)  
- Summe aus iTIMEw Spalte Y wo Spalte N = FALSE
- Erwartung: Nur negative Balances

Test 3.3.3: Cumulative pos. balances (Spalte H)
- Summe aus iTIMEw Spalte Y wo Spalte N = TRUE  
- Erwartung: Nur positive Balances

Test 3.3.4: Below neg. threshold (Spalte I)
- Summe aus iTIMEw Spalte Y wo Spalte O = TRUE
- Erwartung: Werte unter negativem Schwellwert

Test 3.3.5: Above pos. threshold (Spalte J)
- Summe aus iTIMEw Spalte Y wo Spalte P = TRUE
- Erwartung: Werte über positivem Schwellwert

Test 3.3.6: People below neg. threshold (Spalte K)
- Summe aus iTIMEw Spalte AA wo Spalte O = TRUE
- Erwartung: Anzahl Personen unter negativem Schwellwert

Test 3.3.7: People above pos. threshold (Spalte L)
- Summe aus iTIMEw Spalte AA wo Spalte P = TRUE
- Erwartung: Anzahl Personen über positivem Schwellwert
```

### Phase 4: Integration mit anderen Modulen

#### Test 4.1: Slot Closings Integration
```
1. Prüfen Sie Spalten D und E
2. Erwartung: Daten aus fetchSlotClosings sind weiterhin korrekt
3. Vergleichen Sie mit vorherigen Werten (falls verfügbar)
```

#### Test 4.2: Retention Integration  
```
1. Prüfen Sie Spalten M-R
2. Erwartung: Retention-Daten sind in den neuen Spalten korrekt
3. Vergleichen Sie mit vorherigen Werten (falls verfügbar)
```

## Debugging-Schritte

### Bei Fehlern in fetchHourBalances:
```
1. Fügen Sie console.log() Statements hinzu:
   - Nach der Datenquelle-Verbindung
   - In der forEach-Schleife für configData
   - Bei der Woche-Konvertierung
   - Bei der Aggregation

2. Prüfen Sie die Datentypen:
   - Sind die Boolean-Werte in Spalten N, O, P korrekt?
   - Sind die numerischen Werte in Spalten Y, AA korrekt?

3. Testen Sie mit einem einzelnen Country/Location:
   - Kommentieren Sie andere Einträge in der Config aus
   - Führen Sie das Script nur für einen Datensatz aus
```

### Bei Performance-Problemen:
```
1. Messen Sie die Ausführungszeit jedes Moduls
2. Prüfen Sie die Größe der Datenquellen
3. Erwägen Sie Batch-Processing für große Datenmengen
```

## Validierung der Ergebnisse

### Manuelle Stichproben:
```
1. Wählen Sie 2-3 Country/Location/Week Kombinationen
2. Berechnen Sie die Metriken manuell aus iTIMEw
3. Vergleichen Sie mit den Script-Ergebnissen
4. Toleranz: ±0.01 für Rundungsfehler
```

### Plausibilitätsprüfungen:
```
1. Cumulative neg. + pos. balances ≈ Cumulative balances
2. People-Counts sind ganze Zahlen
3. Threshold-Werte sind logisch konsistent
4. Keine negativen People-Counts
```

## Rollback-Plan

Falls Probleme auftreten:
```
1. Stoppen Sie die Script-Ausführung
2. Stellen Sie das Backup wieder her
3. Dokumentieren Sie die Fehler
4. Kehren Sie zum ursprünglichen Script zurück
```

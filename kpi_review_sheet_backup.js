const REPORTING_ID = "1trjbOiLgOzrzv57AAomOfXjFQOmWsRS3cT_tvdRzdhg";
const OPS_REPORTING = "1MHbY6Oz-SiFloV5re7FSHDNqDf4WTMbKtGN1oqbrPcg"; 

function fetchKPIs() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const dataSheet = ss.getSheetByName("Data");

  const mergedMap = new Map();

  const slotClosingsMap = fetchSlotClosings();
  for (const [key, value] of slotClosingsMap.entries()) {
    mergedMap.set(key, value);
  }

  const hourBalancesMap = fetchHourBalances();
  for (const [key, value] of hourBalancesMap.entries()) {
    if (!mergedMap.has(key)) {
      mergedMap.set(key, value);
    } else {
      const existing = mergedMap.get(key);
      existing[5] = value[5];
      existing[6] = value[6];
      existing[7] = value[7];
    }
  }

  const retentionMap = fetchRetention();
  for (const [key, value] of retentionMap.entries()) {
    if (!mergedMap.has(key)) {
      mergedMap.set(key, value);
    } else {
      const existing = mergedMap.get(key);
      existing[8]  = value[8];
      existing[9]  = value[9];
      existing[10] = value[10];
      existing[11] = value[11];
      existing[12] = value[12];
      existing[13] = value[13];
    }
  }

  const result = Array.from(mergedMap.values());
  result.sort((a, b) => parseInt(a[2], 10) - parseInt(b[2], 10)); 

  dataSheet.clearContents();
  const headers = [
    "Country", "Hub", "Week", 
    "Sum deliveries", "Weighted slot closing hrs.", 
    "Cumulative balance", "Cumulative neg. balance", "Cumulative pos. balance",
    "R 12 week pool retention", "R 8 week pool retention", "R 8 week hire retention",
    "R+ 12 week pool retention", "R+ 8 week pool retention", "R+ 8 week hire retention"
  ];
  dataSheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  if (result.length > 0) {
    dataSheet.getRange(2, 1, result.length, headers.length).setValues(result);
  }
}

function fetchSlotClosings() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName("Config");
  const reportingSheet = SpreadsheetApp.openById(REPORTING_ID);
  const sourceSheet = reportingSheet.getSheetByName("iDWH");

  const configData = configSheet.getRange(1, 1, configSheet.getLastRow() - 1, 2).getValues();
  const sourceValues = sourceSheet.getDataRange().getValues();

  const aggregationMap = new Map();

  configData.forEach(([country, location]) => {
    if (!country || !location) return;

    sourceValues.forEach(row => {
      const rowCountry = row[3];
      const rowLocation = row[4];

      if (rowCountry === country && rowLocation === location) {
        const week = row[7];
        const rawM = row[12];
        const rawAR = row[43];
        const sumDeliveries = rawM === "" ? null : parseFloat(rawM);
        const slotClosingHrs = rawAR === "" ? null : parseFloat(rawAR);

        if (sumDeliveries !== null && slotClosingHrs !== null) {
          const key = `${rowCountry}_${rowLocation}_${week}`;

          if (!aggregationMap.has(key)) {
            aggregationMap.set(key, { country: rowCountry, location: rowLocation, week, mSum: 0, weightedArSum: 0 });
          }

          const entry = aggregationMap.get(key);
          entry.mSum += sumDeliveries;
          entry.weightedArSum += sumDeliveries * slotClosingHrs;
        }
      }
    });
  });

  const resultMap = new Map();
  for (const [key, entry] of aggregationMap.entries()) {
    const weightedAr = entry.mSum > 0 ? entry.weightedArSum / entry.mSum : null;
    const row = [entry.country, entry.location, entry.week, entry.mSum, weightedAr, null, null, null, null, null, null, null, null, null];
    resultMap.set(key, row);
  }
  return resultMap;
}

function fetchHourBalances() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName("Config");
  const reportingSheet = SpreadsheetApp.openById(REPORTING_ID);
  const sourceSheet = reportingSheet.getSheetByName("iMinusHours");

  const configData = configSheet.getRange(1, 1, configSheet.getLastRow() - 1, 2).getValues();
  const sourceValues = sourceSheet.getDataRange().getValues();

  const resultMap = new Map();

  configData.forEach(([country, location]) => {
    if (!country || !location) return;

    sourceValues.forEach(row => {
      const rowCountry = row[3];
      const rowLocation = row[4];

      if (rowCountry === country && rowLocation === location) {
        const week = row[7];
        const bal = row[9] === "" ? null : parseFloat(row[9]);
        const neg = row[11] === "" ? null : parseFloat(row[11]);
        const pos = row[12] === "" ? null : parseFloat(row[12]);

        const key = `${rowCountry}_${rowLocation}_${week}`;
        const rowData = [
          rowCountry,
          rowLocation,
          week,
          null,
          null,
          bal,
          neg,
          pos,
          null, null, null,
          null, null, null
        ];

        resultMap.set(key, rowData);
      }
    });
  });

  return resultMap;
}

function fetchRetention() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName("Config");
  const reportingSheet = SpreadsheetApp.openById(OPS_REPORTING);
  const sourceSheet = reportingSheet.getSheetByName("iWD");

  const configData = configSheet.getRange(2, 1, configSheet.getLastRow() - 1, 2).getValues();
  const sourceValues = sourceSheet.getDataRange().getValues();

  const resultMap = new Map();

  configData.forEach(([country, location]) => {
    if (!country || !location) return;

    sourceValues.forEach(row => {
      const rowCountry = row[3];
      const rowLocationRaw = row[9];
      const rowLocation = typeof rowLocationRaw === 'string' ? rowLocationRaw.slice(-3) : String(rowLocationRaw).slice(-3);

      if (rowCountry === country && rowLocation === location) {
        const week = row[6];
        const jobProfile = row[10];
        const emplType = row[11];

        if (emplType === "Total" && (jobProfile === "Runner" || jobProfile === "RunnerPlus")) {
          const ret12Pool = row[26] === "" ? null : parseFloat(row[26]);
          const ret8Pool = row[27] === "" ? null : parseFloat(row[27]);
          const ret8Hire = row[28] === "" ? null : parseFloat(row[28]);

          const key = `${rowCountry}_${location}_${week}`;

          if (!resultMap.has(key)) {
            resultMap.set(key, [
              rowCountry,
              location,
              week,
              null, null, null, null, null,
              null, null, null,
              null, null, null
            ]);
          }

          const entry = resultMap.get(key);

          if (jobProfile === "Runner") {
            entry[8]  = ret12Pool;
            entry[9]  = ret8Pool;
            entry[10] = ret8Hire;
          } else if (jobProfile === "RunnerPlus") {
            entry[11] = ret12Pool;
            entry[12] = ret8Pool;
            entry[13] = ret8Hire;
          }
        }
      }
    });
  });

  return resultMap;
}
